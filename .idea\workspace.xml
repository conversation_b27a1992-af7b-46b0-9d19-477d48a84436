<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="65f5c6dc-693a-4e46-93b3-4f4ffff723aa" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="PerforceDirect.Settings">
    <option name="CHARSET" value="无" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="307cErvj1nlDROLj8VphqfS7hDG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.backtest.executor": "Run",
    "Python.backtestv3.executor": "Run",
    "Python.backtestv4.executor": "Run",
    "Python.backtestv5_ai.executor": "Run",
    "Python.backtestv5_test.executor": "Run",
    "Python.backtestv6.executor": "Run",
    "Python.backtestv7.executor": "Run",
    "Python.debug_ignition_detection.executor": "Run",
    "Python.dynamic_backtest.executor": "Run",
    "Python.dynamic_gap_detector.executor": "Run",
    "Python.fund_flow_query.executor": "Run",
    "Python.fund_flow_query_basic.executor": "Run",
    "Python.get_all_capital_flow_east.executor": "Run",
    "Python.get_all_capital_flow_east_v3.executor": "Run",
    "Python.get_concept_industry_fund_flow.executor": "Run",
    "Python.get_yesterday_zt_pool.executor": "Run",
    "Python.hunter_backtest.executor": "Run",
    "Python.lightning_war_strategy.executor": "Run",
    "Python.main_controller.executor": "Run",
    "Python.market_data_provider.executor": "Run",
    "Python.sector_leader_backtest.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test_SiliconFlow_api.executor": "Run",
    "Python.v2.executor": "Run",
    "Python.v3.executor": "Run",
    "Python.v4.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/stock_dev/llm_analyzer/old/test",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.intellij.platform.ide.impl.presentationAssistant.PresentationAssistantConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="llm_analyzer" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="llm_analyzer" />
      </console-settings>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\stock_dev\llm_analyzer\old\test" />
      <recent name="D:\stock_dev\llm_analyzer\old" />
      <recent name="D:\stock_dev\llm_analyzer\old\data" />
      <recent name="D:\stock_dev\llm_analyzer" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\stock_dev\llm_analyzer\old" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-91d5c284f522-JavaScript-PY-241.15989.155" />
        <option value="bundled-python-sdk-babbdf50b680-7c6932dee5e4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.15989.155" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="65f5c6dc-693a-4e46-93b3-4f4ffff723aa" name="更改" comment="" />
      <created>1752981942612</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752981942612</updated>
      <workItem from="1752981943692" duration="66708000" />
      <workItem from="1753091730912" duration="86037000" />
      <workItem from="1753327189036" duration="1556000" />
      <workItem from="1753330125853" duration="259000" />
      <workItem from="1753331364015" duration="1000" />
      <workItem from="1753331371157" duration="1000" />
      <workItem from="1753331379052" duration="6000" />
      <workItem from="1753331748706" duration="23000" />
      <workItem from="1753333844658" duration="3803000" />
      <workItem from="1753340500203" duration="30000" />
      <workItem from="1753340568383" duration="1287000" />
      <workItem from="1753348081407" duration="1367000" />
      <workItem from="1753365048419" duration="877000" />
      <workItem from="1753365949584" duration="1042000" />
      <workItem from="1753367015778" duration="32653000" />
      <workItem from="1753486689625" duration="240000" />
      <workItem from="1753486991677" duration="20000" />
      <workItem from="1753487128521" duration="49505000" />
      <workItem from="1753624422058" duration="204178000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".env" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/llm_analyzer$v4.coverage" NAME="v4 覆盖结果" MODIFIED="1753370902573" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv5_test.coverage" NAME="backtestv5_test 覆盖结果" MODIFIED="1753597249183" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$dynamic_gap_detector.coverage" NAME="dynamic_gap_detector 覆盖结果" MODIFIED="1754017666845" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$get_all_capital_flow_east.coverage" NAME="get_all_capital_flow_east 覆盖结果" MODIFIED="1753372853360" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$test_SiliconFlow_api.coverage" NAME="test_SiliconFlow_api 覆盖结果" MODIFIED="1753741253385" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$get_yesterday_zt_pool.coverage" NAME="get_yesterday_zt_pool 覆盖结果" MODIFIED="1753515356758" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/llm_analyzer$test.coverage" NAME="test 覆盖结果" MODIFIED="1753008088951" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/llm_analyzer$hunter_backtest.coverage" NAME="hunter_backtest 覆盖结果" MODIFIED="1753742731237" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$v2.coverage" NAME="v2 覆盖结果" MODIFIED="1753284130555" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$get_concept_industry_fund_flow.coverage" NAME="get_concept_industry_fund_flow 覆盖结果" MODIFIED="1754011548698" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$market_data_provider.coverage" NAME="market_data_provider 覆盖结果" MODIFIED="1753113673047" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/llm_analyzer$fund_flow_query_basic.coverage" NAME="fund_flow_query_basic 覆盖结果" MODIFIED="1753777288160" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv6.coverage" NAME="backtestv6 覆盖结果" MODIFIED="1754028383641" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$dynamic_backtest.coverage" NAME="dynamic_backtest 覆盖结果" MODIFIED="1753971711875" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv3.coverage" NAME="backtestv3 覆盖结果" MODIFIED="1753374535634" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv5_ai.coverage" NAME="backtestv5_ai 覆盖结果" MODIFIED="1753512465968" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$v3.coverage" NAME="v3 覆盖结果" MODIFIED="1753286557251" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$debug_ignition_detection.coverage" NAME="debug_ignition_detection 覆盖结果" MODIFIED="1753677306929" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$get_all_capital_flow_east_v3.coverage" NAME="get_all_capital_flow_east_v3 覆盖结果" MODIFIED="1753673876106" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv4.coverage" NAME="backtestv4 覆盖结果" MODIFIED="1753597263319" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtestv7.coverage" NAME="backtestv7 覆盖结果" MODIFIED="1754017127909" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$lightning_war_strategy.coverage" NAME="lightning_war_strategy 覆盖结果" MODIFIED="1754025832594" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$backtest.coverage" NAME="backtest 覆盖结果" MODIFIED="1753369706107" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old" />
    <SUITE FILE_PATH="coverage/llm_analyzer$sector_leader_backtest.coverage" NAME="sector_leader_backtest 覆盖结果" MODIFIED="1753715894444" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
    <SUITE FILE_PATH="coverage/llm_analyzer$main_controller.coverage" NAME="main_controller 覆盖结果" MODIFIED="1753838843723" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/llm_analyzer$fund_flow_query.coverage" NAME="fund_flow_query 覆盖结果" MODIFIED="1753749127955" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/old/test" />
  </component>
</project>